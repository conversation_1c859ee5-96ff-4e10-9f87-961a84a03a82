from ultralytics import YOLO
from Globals import *
from test_for_function import test_for_exchange_teacher, test_for_exchange_student, test_for_exchange_student_back
import torch
if __name__ == '__main__':

    # 进行原版模型的正常训练：
    if not bool_distill:
        # 加载模型
        model = YOLO(model_file)  # 从头开始构建新模型

        # 训练模型
        model.train(data=dataset, epochs=epochs, save=bool_save, save_period=save_period, resume=bool_resume, device=device, batch=batch_size)

        # 在验证集上评估模型性能
        metrics = model.val(data=dataset)

        # 对图像进行预测
        # results = model(test_graph)

    # 进行蒸馏训练：
    elif bool_distill:
        # P0:第一阶段准备阶段
        if phase == 0:
            print("🔄 开始准备教师模型权重...")
            # [1]可选:将学生预训练模型转变为适合的格式
            # test_for_exchange_student(path_student, path_student_prepare)
            # [2]将教师预训练模型转变为适合的格式
            test_for_exchange_teacher(path_teacher, path_teacher_prepare)
            print("✅ 教师模型权重准备完成")
        # P1:训练开始
        if phase == 1:
            print("🚀 开始知识蒸馏训练...")
            # [1]加载知识蒸馏模型架构
            model = YOLO(model_file)
            print(f"📊 模型架构加载完成，参数数量: {sum(p.numel() for p in model.model.parameters())}")

            # [2]可选:加载预训练学生模型
            # model.load_state_dict(torch.load(path_student_prepare), strict=False)

            # [3]加载预训练教师模型 - 修复权重传递
            print("🔄 正在加载教师权重...")
            teacher_weights = torch.load(path_teacher_prepare)
            print(f"📊 教师权重文件包含 {len(teacher_weights)} 个参数")

            # 使用更严格的权重加载策略
            model_dict = model.model.state_dict()
            print(f"📊 目标模型包含 {len(model_dict)} 个参数")

            # 统计匹配情况
            matched_params = 0
            mismatched_params = 0
            missing_params = 0

            print("🔍 开始检查权重匹配情况...")
            for name, param in teacher_weights.items():
                if name in model_dict:
                    if param.shape == model_dict[name].shape:
                        model_dict[name] = param
                        matched_params += 1
                    else:
                        print(f"⚠️  形状不匹配: {name} - 教师: {param.shape}, 学生: {model_dict[name].shape}")
                        mismatched_params += 1
                else:
                    print(f"❌ 缺失参数: {name}")
                    missing_params += 1

            # 加载修正后的权重
            model.model.load_state_dict(model_dict, strict=True)
            print(f"✅ 权重加载完成: {matched_params} 匹配, {mismatched_params} 形状不匹配, {missing_params} 缺失")

            # 如果有形状不匹配的参数，停止执行以便检查
            if mismatched_params > 0:
                print("❌ 发现形状不匹配的参数，请检查模型配置")
                exit(1)

            # [4]进行模型训练 - 使用更保守的参数避免CUDA错误
            try:
                # 清理GPU缓存
                torch.cuda.empty_cache()

                model.train(
                    data=dataset,
                    epochs=epochs,
                    save=bool_save,
                    save_period=save_period,
                    freeze=teacher_peer_list,
                    resume=bool_resume,
                    device=device,
                    batch=max(4, batch_size-2),  # 进一步降低batch size
                    amp=False,  # 禁用混合精度训练避免cuDNN错误
                    workers=4,  # 减少数据加载器工作进程
                    cache=False,  # 禁用缓存
                    deterministic=True,  # 确保确定性训练
                    project="runs/detect",
                    name="train56_fixed"
                )
            except Exception as e:
                print(f"❌ 训练过程中出现错误: {e}")
                # 清理GPU缓存
                torch.cuda.empty_cache()
                raise

            # [5]将训练好得模型提取出来
            test_for_exchange_student_back(model_based_file, model, final)
            # [6]可选:检验训练集准确度
            model.val(data=dataset)
            # [7]可选:要求程序结束时自动关机以节省运算资源
            # import os
            # os.system("shutdown /s /t 0")

